import {
  ContractType,
  type CreditSettingCalculator,
  UsersignInMethod,
} from 'api/core/generated';
import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  AppRoutePaths,
  AppSearchParams,
  BANKLINK_PAYMENT_POLL_STATUSES,
  FormFieldNames,
  FormValidationErrorTypes,
  GoogleAnalyticsEvents,
  HirePurchaseRoutePaths,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  NEGATIVE_ELIGIBILITY_STATUSES,
  PageAttributeNames,
  PAYSERA_PAYMENT_STATUSES,
  PURCHASE_FLOW_LOG_ACTIONS,
  SigningPageViewTypes,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { coreApiUrl, onlyPasswordSigningEnabled } from 'environment';
import { useGetBanklinkPaymentStatus } from 'hooks/use-banklink-payment-status-poll';
import { useBanklinkSigningPoll } from 'hooks/use-banklink-signing-poll';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useGetCreditSettings } from 'hooks/use-get-credit-settings';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetFirstDueAt } from 'hooks/use-get-first-due-at';
import { useGetPageAttributes } from 'hooks/use-get-page-attributes';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useLogout } from 'hooks/use-logout';
import { useSignContractWithBanklink } from 'hooks/use-sign-contract-with-banklink';
import { useSignContractWithIdCard } from 'hooks/use-sign-contract-with-id-card';
import { useSignContractWithMobileId } from 'hooks/use-sign-contract-with-mobile-id';
import { useSignContractWithPassword } from 'hooks/use-sign-contract-with-password';
import { useSignContractWithSmartId } from 'hooks/use-sign-contract-with-smart-id';
import { useUpdateApplicationCampaign } from 'hooks/use-update-application-campaign-mutation';
import { useUpdateApplicationCreditInfo } from 'hooks/use-update-application-credit-info';
import { useDebounce, useEffectOnce, useLocalStorage } from 'hooks/utils';
import type { HirePurchaseSigningPageLogic } from 'models';
import { useEffect, useMemo, useState } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  createSearchParams,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  clearInvalidDownPaymentAmountError,
  convertPageAttributeNamesToObject,
  getApplicationDefaultDownPayment,
  getCurrentCreditSetting,
  getDownPayment,
  getFilteredUrlSearchParamsObject,
  getUpdateApplicationCreditInfoVariables,
  loadAndInitializeIdCardScripts,
  removeFromStorage,
  setToStorage,
  validateDownPayment,
} from 'services';

declare let iSignApplet: {
  init: (options: Record<string, unknown>) => void;
  getCertificate: (options: Record<string, unknown>) => void;
  setHashAlgorithm: (algorithm: string) => void;
  sign: (
    dtbs: string,
    dtbsHash: string,
    callback: (signedHash: string) => void,
  ) => void;
};

export const useSigningPageLogic = (): HirePurchaseSigningPageLogic => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const payseraSigningPaymentStatus = searchParams.get(
    AppSearchParams.payseraSigningPaymentStatus,
  );
  const { t: te, i18n } = useTranslation(LocizeNamespaces.errors);
  const {
    user,
    getUser,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    applicationPrivateInfo,
    trackGoogleAnalyticsEvent,
  } = useRootContext();
  const { logOut } = useLogout();
  const { logAction } = useLogApplicationAction();
  const { signContractWithPassword, signContractWithPasswordProcessing } =
    useSignContractWithPassword();
  const {
    signContractWithSmartId,
    prepareSmartIdContractSignature,
    smartIdContractSignaturePreparationChallenge,
    smartIdContractSignaturePreparationLoading,
    smartIdContractSignaturePreparationError,
  } = useSignContractWithSmartId();
  const { signContractWithIdCard, prepareIdCardContractSignature } =
    useSignContractWithIdCard();
  const {
    signContractWithMobileId,
    prepareMobileIdContractSignature,
    mobileIdContractSignaturePreparationChallenge,
    mobileIdContractSignaturePreparationLoading,
    mobileIdContractSignaturePreparationError,
  } = useSignContractWithMobileId();
  const {
    prepareBanklinkContractSignature,
    banklinkSigningProcessing,
    banklinkSigningAcceptUrl,
    banklinkSigningCancelUrl,
  } = useSignContractWithBanklink();
  const {
    banklinkPaymentStatus,
    startBanklinkPaymentStatusPolling,
    stopBanklinkPaymentStatusPolling,
  } = useGetBanklinkPaymentStatus();
  const {
    isBanklinkSigningPollSuccess,
    startBanklinkSigningStatusPolling,
    stopBanklinkSigningStatusPolling,
  } = useBanklinkSigningPoll();
  const { pageAttributes, pageAttributesLoading, getPageAttributes } =
    useGetPageAttributes();
  const { getBanklinks, banklinks } = useGetBanks();
  const { getCreditSettings, creditSettings, creditSettingsLoading } =
    useGetCreditSettings();
  const { updateApplicationCreditInfo, applicationCreditInfoUpdating } =
    useUpdateApplicationCreditInfo();
  const { updateApplicationCampaign, applicationCampaignUpdating } =
    useUpdateApplicationCampaign();
  const { firstDueAtDate, firstDueAtDateLoading, getFirstDueAt } =
    useGetFirstDueAt();

  const {
    application,
    applicationReferenceKey,
    applicationLoading,
    getApplication,
  } = useGetCurrentApplication({
    onCompleted: (data) => {
      if (data.application?.id) {
        const paymentLeaveEnabled = Boolean(
          application.campaign?.payment_leave_enabled,
        );

        getFirstDueAt({
          application_id: data.application.id,
          has_payment_leave: paymentLeaveEnabled,
        });

        setIsPaymentLeaveEnabled(paymentLeaveEnabled);

        if (data.application.credit_info) {
          setSelectedCreditSetting(null);

          const { net_total, down_payment } = data.application.credit_info;

          getCreditSettings({
            net_total,
            down_payment,
            application_id: data.application.id,
          });

          setDownPaymentFieldValue(down_payment);
        }

        if (
          NEGATIVE_ELIGIBILITY_STATUSES.includes(
            data.application.simple_eligibility_status,
          )
        ) {
          logAction({
            productId: data.application.id,
            action: PURCHASE_FLOW_LOG_ACTIONS.rejected,
          });
        } else {
          logAction({
            productId: data.application.id,
            action: PURCHASE_FLOW_LOG_ACTIONS.signingPage,
          });
        }
      }
    },
  });

  const [storedQueryParams, storeQueryParams] = useLocalStorage<
    Nullable<Record<string, string>>
  >(LocalStorageKeys.storedQueryParams, null);
  const [downPaymentFieldValue, setDownPaymentFieldValue] = useState(0);
  // this is for optimistic update when we have no changes in credit settings recalculation
  const [selectedCreditSetting, setSelectedCreditSetting] =
    useState<Nullable<CreditSettingCalculator>>(null);
  const [signingPageViewType, setSigningPageViewType] = useState(
    SigningPageViewTypes.signing,
  );
  const [isBanklinkOptionSelected, setIsBanklinkOptionSelected] =
    useState(false);
  const [
    signContractWithIDCardProcessing,
    setSignContractWithIDCardProcessing,
  ] = useState(false);
  const selectBanklinkOption = () => setIsBanklinkOptionSelected(true);
  const [isPaymentLeaveEnabled, setIsPaymentLeaveEnabled] = useState(false);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const shouldShowDownPaymentField = Boolean(
    visiblePageAttributes[PageAttributeNames.downPayment] &&
      applicationPrivateInfo?.show_down_payment &&
      creditSettings?.length &&
      !application.from_retail,
  );

  const periodValues =
    creditSettings?.map((setting) => setting?.month || 0) || [];

  const signInMethod = application.user?.sign_in_method ?? null;

  const signingPageLoaded =
    Boolean(creditSettings) &&
    !pageUrlAndNavigationProcessing &&
    !pageAttributesLoading &&
    ((Boolean(application.id) && applicationLoading) || !applicationLoading);

  const userCanSignContract =
    application.is_test || signInMethod !== UsersignInMethod.PASSWORD;

  const signingPageFormConfig = {
    defaultValues: {
      [FormFieldNames.periodMonths]:
        application.credit_info?.period_months ?? 0,
      [FormFieldNames.downPayment]:
        getApplicationDefaultDownPayment(application),
    },
  };

  const contractLink = `${coreApiUrl}/application/${application.id}/generate-contract?contract-type=${ContractType.APPLICATION_SIGNED}`;

  const onPinConfirmationCancel = () => {
    setSigningPageViewType(SigningPageViewTypes.signing);
  };

  const onLogOutClick = () => {
    logOut().then(() => {
      getUser();
      getPageUrlAndNavigate();
    });
  };

  const logSuccessfulSigningAction = () => {
    // LOGGING ACTION

    if (application.id) {
      logAction({
        productId: application.id,
        action: PURCHASE_FLOW_LOG_ACTIONS.successfullySigned,
      });
    }
  };

  const executeSuccessfulSigningCallbacks = () => {
    trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contractSigned);
    logSuccessfulSigningAction();
    navigate(
      `/${AppRoutePaths.HIRE_PURCHASE}/${HirePurchaseRoutePaths.SUCCESS}?reference_key=${applicationReferenceKey}`,
    );
    // getPageUrlAndNavigate(true);
  };

  const getMinMaxDownPayment = () => {
    const applicationDefaultDownPayment = Number(
      application?.credit_info?.min_down_payment,
    );

    const { min_down_payment, max_down_payment } =
      selectedCreditSetting ||
      getCurrentCreditSetting(
        creditSettings,
        application.credit_info?.period_months || 0,
      ) ||
      {};

    const creditSettingMinDownPayment = getDownPayment(min_down_payment);

    const minDownPayment =
      applicationDefaultDownPayment > creditSettingMinDownPayment
        ? applicationDefaultDownPayment
        : creditSettingMinDownPayment;

    return { minDownPayment, maxDownPayment: max_down_payment };
  };

  const signAppWithPassword = () => {
    signContractWithPassword({
      application_id: application.id,
      contract_type: ContractType.APPLICATION_SIGNED,
    }).then(() => {
      executeSuccessfulSigningCallbacks();
    });
  };

  const prepareSigningAppWithSmartId = () => {
    prepareSmartIdContractSignature({
      application_id: application.id,
      contract_type: ContractType.APPLICATION_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
  };

  const signAppWithIdCard = () => {
    const lang = user?.language_abbr.split('-')[0];

    iSignApplet.init({
      certificatePurpose: 'sign',
      codebase: `${window.location.protocol}//${window.location.host}/dokobit`,
      language: lang,
      supportedResidencies: ['ee'],
    });

    setSignContractWithIDCardProcessing(true);

    (
      window as unknown as Window & {
        certificateSelected: (certificate: string) => void;
      }
    ).certificateSelected = (certificate: string) => {
      const cert = btoa(unescape(encodeURIComponent(certificate)));
      prepareIdCardContractSignature({
        application_id: application.id,
        certificate: cert,
        contract_type: ContractType.APPLICATION_SIGNED,
      })
        .then(({ data: prepareSignatureData }) => {
          iSignApplet.setHashAlgorithm(
            prepareSignatureData?.challenge?.algorithm || '',
          );

          iSignApplet.sign(
            prepareSignatureData?.challenge?.dtbs || '',
            prepareSignatureData?.challenge?.dtbs_hash || '',
            (signedHash: string) => {
              signContractWithIdCard({
                token: prepareSignatureData?.challenge?.token || '',
                signature: signedHash,
              })
                .then(({ data }) => {
                  if (data?.success) {
                    executeSuccessfulSigningCallbacks();
                  } else {
                    toast.error(
                      te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError),
                    );
                  }
                })
                .catch(() => {
                  setSignContractWithIDCardProcessing(false);
                });
            },
          );
        })
        .catch(() => {
          setSignContractWithIDCardProcessing(false);
          toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        });
    };
  };

  const prepareSigningAppWithMobileId = () => {
    prepareMobileIdContractSignature({
      application_id: application.id,
      contract_type: ContractType.APPLICATION_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
  };

  const signAppWithMobileId = () =>
    signContractWithMobileId().then(({ data }) => {
      if (data?.success) {
        executeSuccessfulSigningCallbacks();
      } else {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      }
    });

  const signAppWithSmartId = () =>
    signContractWithSmartId().then(({ data }) => {
      if (data?.success) {
        executeSuccessfulSigningCallbacks();
      } else {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      }
    });

  const signAppByMobileIdOrSmartId = () => {
    if (signInMethod === UsersignInMethod.MOBILE) {
      return signAppWithMobileId();
    }
    // default is smart id
    return signAppWithSmartId();
  };

  const signAppWithBanklink = ({ payment_method_key }: FieldValues) => {
    prepareBanklinkContractSignature({
      application_id: application.id,
      contract_type: ContractType.APPLICATION_SIGNED,
      payment_method_key,
      accept_url: banklinkSigningAcceptUrl,
      cancel_url: banklinkSigningCancelUrl,
    }).then(({ data }) => {
      const redirectUrl = data?.challenge?.redirect_url;
      const sessionId = data?.challenge?.session_id;

      const queryParamsToStore = getFilteredUrlSearchParamsObject(
        {
          [AppSearchParams.referenceKey]: true,
        },
        searchParams,
      );

      if (Object.keys(queryParamsToStore).length) {
        storeQueryParams(queryParamsToStore);
      }

      if (sessionId) {
        setToStorage(LocalStorageKeys.sessionId, sessionId);
      }

      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
    });
  };

  const onSigningFormSubmit = async (formFieldValues: FieldValues) => {
    try {
      // if application is converted to the one with high IRR
      // should send to BE that user accepted the conversion offer
      if (application?.pre_signing_conversion_offer) {
        await updateApplicationCreditInfo({
          application_id: application.id,
          period_months:
            Number(formFieldValues.period_months) ||
            (application.credit_info?.period_months ?? 0),
          accept_conversion_offer: true,
        });
      }

      if (onlyPasswordSigningEnabled) {
        signAppWithPassword();
        return;
      }

      switch (signInMethod) {
        case UsersignInMethod.PAYSERA_BANKLINK:
        case UsersignInMethod.MAGIC_LINK:
          signAppWithBanklink(formFieldValues);
          break;
        case UsersignInMethod.MOBILE:
          prepareSigningAppWithMobileId();
          break;
        case UsersignInMethod.SMART_ID:
          prepareSigningAppWithSmartId();
          break;
        case UsersignInMethod.ID_CARD:
          signAppWithIdCard();
          break;
        default:
          signAppWithPassword();
          break;
      }
    } catch {
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  };

  const onTogglePaymentLeaveOffer = () => {
    updateApplicationCampaign({
      applicationId: application.id,
      referenceKey: applicationReferenceKey,
      paymentLeaveEnabled: !isPaymentLeaveEnabled,
    }).then(() => {
      getFirstDueAt({
        application_id: application.id,
        has_payment_leave: !isPaymentLeaveEnabled,
      }).then(() => {
        setIsPaymentLeaveEnabled(!isPaymentLeaveEnabled);
      });
    });
  };

  const onDownPaymentChange = async (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => {
    const { down_payment: downPayment, period_months: periodMonths } =
      formFieldValues;
    const { setError } = formMethods;

    const downPaymentIsValid = validateDownPayment({
      downPayment,
      application,
      creditSetting: getCurrentCreditSetting(creditSettings, periodMonths),
    });

    if (downPaymentIsValid) {
      setDownPaymentFieldValue(Number(downPayment));
      clearInvalidDownPaymentAmountError(formMethods);
      try {
        await updateApplicationCreditInfo(
          getUpdateApplicationCreditInfoVariables({
            application,
            applicationReferenceKey,
            downPayment,
            periodMonths,
          }),
        );
        getApplication();
      } catch {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      }
    } else {
      setError(FormFieldNames.downPayment, {
        type: FormValidationErrorTypes.invalidDownPaymentAmount,
      });
    }
  };

  const debouncedDownPaymentChange = useDebounce(onDownPaymentChange, 400);

  const onPeriodChange = async (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => {
    if (visiblePageAttributes[PageAttributeNames.downPayment]) {
      if (downPaymentFieldValue === +formFieldValues.down_payment) {
        try {
          setSelectedCreditSetting(
            getCurrentCreditSetting(
              creditSettings,
              +formFieldValues.period_months,
            ) ?? null,
          );
          await updateApplicationCreditInfo(
            getUpdateApplicationCreditInfoVariables({
              application,
              applicationReferenceKey,
              downPayment: downPaymentFieldValue,
              periodMonths: +formFieldValues.period_months,
            }),
          );
        } catch {
          toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
          setSelectedCreditSetting(null);
        }
        return;
      }

      onDownPaymentChange(formFieldValues, formMethods);
    }
  };

  useEffectOnce(() => {
    if (storedQueryParams) {
      const params = Object.fromEntries(Array.from(searchParams));
      navigate(
        {
          search: createSearchParams({
            ...params,
            ...storedQueryParams,
          }).toString(),
        },
        { replace: true },
      );
      storeQueryParams(null);
    }
  });

  useEffectOnce(() => {
    getPageAttributes();
  });

  useEffect(() => {
    if (isBanklinkSigningPollSuccess) {
      stopBanklinkSigningStatusPolling();
      executeSuccessfulSigningCallbacks();
    }
  }, [isBanklinkSigningPollSuccess]);

  useEffectOnce(() => {
    getApplication();
  });

  useEffect(() => {
    if (
      (visiblePageAttributes[PageAttributeNames.banklink] ||
        signInMethod === UsersignInMethod.MAGIC_LINK) &&
      !banklinks.length
    ) {
      getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
    }
  }, [pageAttributes?.length]);

  useEffectOnce(() => {
    if (payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
      setSigningPageViewType(SigningPageViewTypes.pending);
      startBanklinkPaymentStatusPolling().catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
    } else if (
      payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed
    ) {
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  });

  useEffect(() => {
    switch (banklinkPaymentStatus) {
      case BANKLINK_PAYMENT_POLL_STATUSES.success:
        stopBanklinkPaymentStatusPolling();
        startBanklinkSigningStatusPolling()
          .then(({ data }) => {
            if (data?.success) {
              stopBanklinkSigningStatusPolling();
              removeFromStorage(LocalStorageKeys.sessionId);
              executeSuccessfulSigningCallbacks();
            }
          })
          .catch((error) => {
            // Stop polling only if the code is not 3803
            // 3803 means that the Payment was not confirmed by Paysera
            if (error.code !== 3803) {
              stopBanklinkPaymentStatusPolling();
            }
          });
        break;
      case BANKLINK_PAYMENT_POLL_STATUSES.failed:
        stopBanklinkPaymentStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        setSigningPageViewType(SigningPageViewTypes.signing);
        break;
      default:
        break;
    }
  }, [banklinkPaymentStatus]);

  useEffect(() => {
    if (signInMethod !== UsersignInMethod.ID_CARD) {
      loadAndInitializeIdCardScripts();
    }
  }, [signInMethod]);

  return useMemo(
    () => ({
      signingPageLoaded,
      prepareContractSigning:
        smartIdContractSignaturePreparationLoading ||
        mobileIdContractSignaturePreparationLoading,
      processingSigningPage:
        pageUrlAndNavigationProcessing ||
        signContractWithPasswordProcessing ||
        signContractWithIDCardProcessing ||
        banklinkSigningProcessing,
      visiblePageAttributes,
      getMinMaxDownPayment,
      onSigningFormSubmit,
      debouncedDownPaymentChange,
      signingPageFormConfig,
      contractLink,
      userCanSignContract,
      downPaymentFieldValue,
      applicationCreditInfoUpdating,
      smartIdSigningChallengeViewIsVisible:
        Boolean(smartIdContractSignaturePreparationChallenge) &&
        !smartIdContractSignaturePreparationError,
      mobileIdSigningChallengeViewIsVisible:
        Boolean(mobileIdContractSignaturePreparationChallenge) &&
        !mobileIdContractSignaturePreparationError,
      smartIdContractSignaturePollChallengeId:
        smartIdContractSignaturePreparationChallenge?.challenge_id || '',
      mobileIdContractSignaturePollChallengeId:
        mobileIdContractSignaturePreparationChallenge?.challenge_id || '',
      banklinkOptions: banklinks,
      signingPageViewType,
      shouldShowDownPaymentField,
      isBanklinkOptionSelected,
      selectBanklinkOption,
      onPinConfirmationCancel,
      signInMethod,
      onLogOutClick,
      application,
      onTogglePaymentLeaveOffer,
      isPaymentLeaveEnabled,
      applicationCampaignUpdating,
      firstDueAtDate,
      firstDueAtDateLoading,
      signAppByMobileIdOrSmartId,
      periodValues,
      onPeriodChange,
      selectedCreditSetting,
      updatingApplicationConditions:
        applicationLoading || creditSettingsLoading || firstDueAtDateLoading,
    }),

    [
      signingPageLoaded,
      pageUrlAndNavigationProcessing,
      signContractWithPasswordProcessing,
      smartIdContractSignaturePreparationLoading,
      signContractWithIDCardProcessing,
      mobileIdContractSignaturePreparationLoading,
      banklinkSigningProcessing,
      visiblePageAttributes,
      getMinMaxDownPayment,
      onSigningFormSubmit,
      debouncedDownPaymentChange,
      signingPageFormConfig,
      contractLink,
      userCanSignContract,
      downPaymentFieldValue,
      applicationCreditInfoUpdating,
      mobileIdContractSignaturePreparationChallenge,
      smartIdContractSignaturePreparationChallenge,
      mobileIdContractSignaturePreparationChallenge?.challenge_id,
      smartIdContractSignaturePreparationChallenge?.challenge_id,
      mobileIdContractSignaturePreparationError,
      smartIdContractSignaturePreparationError,
      signInMethod,
      banklinks,
      signingPageViewType,
      shouldShowDownPaymentField,
      isBanklinkOptionSelected,
      selectBanklinkOption,
      onPinConfirmationCancel,
      onLogOutClick,
      application,
      onTogglePaymentLeaveOffer,
      isPaymentLeaveEnabled,
      applicationCampaignUpdating,
      firstDueAtDate,
      firstDueAtDateLoading,
      signAppByMobileIdOrSmartId,
      periodValues,
      onPeriodChange,
      selectedCreditSetting,
      creditSettingsLoading,
      applicationLoading,
    ],
  );
};
